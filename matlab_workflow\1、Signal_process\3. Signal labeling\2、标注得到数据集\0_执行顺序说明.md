# 标注得到数据集 - 执行顺序说明

## 📋 文件夹功能概述

本文件夹用于从标注结果中提取信号片段，构建训练数据集。主要功能包括读取标注文件、根据ROILimits提取对应时间段的信号、按标签类型分类保存信号片段，以及生成处理报告和统计信息。

## 🔢 脚本执行顺序

按照数字序号顺序执行，确保数据处理的正确性和完整性：

### 1️⃣ 第一步：测试提取功能
**1_test_extraction.m** - 提取功能测试
- **功能**：测试信号片段提取功能是否正常工作
- **用途**：验证算法逻辑，确保提取过程无误
- **输入**：测试用的标注文件和信号文件
- **输出**：测试结果和功能验证报告
- **建议**：首次使用或修改算法后必须运行

### 2️⃣ 第二步：执行标注数据处理
**2_label_process.m** - 标注数据处理主程序
- **功能**：从标注文件中提取信号片段的核心处理程序
- **用途**：批量处理标注数据，生成结构化的信号片段
- **输入**：
  - `4、Label/` 文件夹中的标注文件（.mat格式的labeledSignalSet）
  - `1、Raw data/` 文件夹中的原始分段信号文件
- **输出**：
  - `2、Processed data/` 文件夹中的分类信号片段
  - 按标签类型（SB、MB、CRS等）分类保存
- **建议**：这是主要的数据处理脚本

### 3️⃣ 第三步：验证提取结果
**3_validate_extraction.m** - 提取结果验证
- **功能**：验证信号片段提取的准确性和完整性
- **用途**：质量控制，确保提取的数据符合预期
- **输入**：`2、Processed data/` 中的处理结果
- **输出**：验证报告和质量评估结果
- **建议**：每次处理后都应该运行以确保数据质量

### 4️⃣ 第四步：演示和可视化
**4_demo_usage.m** - 使用示例和可视化
- **功能**：展示处理结果，提供数据集统计和可视化
- **用途**：查看处理效果，生成统计报告
- **输入**：处理完成的数据集
- **输出**：
  - 数据集统计信息
  - 可视化图表
  - 使用示例演示
- **建议**：用于结果展示和质量评估

## 📁 文件夹结构说明

```
2、标注得到数据集/
├── 0_执行顺序说明.md          # 本说明文件
├── 1_test_extraction.m        # 提取功能测试
├── 2_label_process.m          # 标注数据处理主程序 ⭐
├── 3_validate_extraction.m    # 提取结果验证
├── 4_demo_usage.m             # 使用示例和可视化
├── 1、Raw data/               # 原始分段信号文件
├── 2、Processed data/         # 提取的标注信号片段
├── 3、Backup/                 # 备份文件夹
├── 4、Label/                  # 标注文件存储
└── code_analysis_report.md    # 代码分析报告
```

## 🎯 推荐使用流程

### 标准处理流程
```matlab
% 1. 测试提取功能
run('1_test_extraction.m')

% 2. 执行主要处理
run('2_label_process.m')

% 3. 验证结果
run('3_validate_extraction.m')

% 4. 查看结果和统计
run('4_demo_usage.m')
```

### 快速处理（有经验用户）
```matlab
% 直接执行主程序
run('2_label_process.m')

% 验证结果
run('3_validate_extraction.m')
```

### 调试和开发
```matlab
% 先测试功能
run('1_test_extraction.m')

% 查看演示了解数据结构
run('4_demo_usage.m')

% 再执行处理
run('2_label_process.m')
```

## ⚙️ 处理参数说明

### 主要处理参数
- **时间恢复**：支持时间信息的准确恢复
- **多通道支持**：处理多通道信号数据
- **批量提取**：支持批量处理多个文件
- **质量检查**：内置数据质量检查机制

### 输入要求
- 标注文件：labeledSignalSet格式的.mat文件
- 信号文件：预处理后的分段信号数据
- 文件命名：遵循标准命名规范

### 输出格式
- 信号片段：按标签类型分类的.mat文件
- 命名格式：`{dataset}_seg{XXX}_{channel}_{label}_{index}.mat`
- 统计报告：处理过程的详细统计信息

## 🔧 故障排除

### 常见问题
1. **标注文件格式错误**
   - 检查标注文件是否为labeledSignalSet格式
   - 确认文件路径正确

2. **信号文件缺失**
   - 检查`1、Raw data/`文件夹中是否有对应的信号文件
   - 验证文件命名是否匹配

3. **提取结果异常**
   - 运行`1_test_extraction.m`测试功能
   - 检查ROILimits时间范围是否合理

4. **内存不足**
   - 减少批处理文件数量
   - 分批处理大数据集

### 解决步骤
1. 运行`1_test_extraction.m`诊断问题
2. 检查输入文件的完整性和格式
3. 查看生成的日志和错误信息
4. 参考`code_analysis_report.md`了解详细功能

## 📊 质量控制

### 验证检查点
- 提取片段数量是否符合预期
- 时间对齐是否准确
- 标签分类是否正确
- 数据完整性检查

### 性能指标
- 处理速度和效率
- 内存使用情况
- 错误率和成功率
- 数据质量评分

---

**创建时间**: 2024-08-22  
**版本**: 1.0  
**说明**: 此文件提供了标注数据处理的完整执行指导
