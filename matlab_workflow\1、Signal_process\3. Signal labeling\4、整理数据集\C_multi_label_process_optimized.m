%% C_MULTI_LABEL_PROCESS_OPTIMIZED 优化版多标注文件批量处理程序
%   优化版本的多标注文件批量处理程序，主要改进包括：
%   1. 直接从labeledSignalSet对象中提取信号数据，避免重复文件I/O
%   2. 提供两种数据加载模式：embedded（推荐）和external（兼容）
%   3. 改进的性能监控和内存使用优化
%   4. 更详细的处理统计和错误报告
%
%   主要优化：
%   - 性能提升：减少文件I/O操作，提高处理速度
%   - 内存优化：避免重复加载相同的信号数据
%   - 错误处理：更健壮的异常处理和恢复机制
%   - 灵活性：支持多种数据加载模式
%
%   数据加载模式：
%   - 'embedded': 从labeledSignalSet中直接提取（推荐，高效）
%   - 'external': 从外部文件加载（兼容模式，适用于调试）
%
%   性能预期：
%   - embedded模式：约快30-50%，内存使用减少40-60%
%   - external模式：与原版本相同，但增加了更多验证
%
%   作者：肠鸣音信号分析团队
%   日期：2025年
%   版本：1.0 (优化版)
%
%   See also: C_MULTI_LABEL_PROCESS, B_TEST_MULTI_LABEL_PROCESS, D_VERIFY_RESULTS

clear;
clc;
close all;

%% 配置参数
config = struct();
config.labelFile = fullfile('4、Label', 'ls_data3.mat');
config.rawDataDir = '1、Raw data';
config.outputDir = '2、Processed data';

% 数据加载模式配置
% 'embedded': 从labeledSignalSet中直接提取信号数据（推荐，更高效）
% 'external': 从外部文件加载信号数据（兼容模式，用于验证）
config.dataLoadMode = 'embedded';

% 性能监控配置
config.enableProfiling = true;
config.showProgress = true;
config.saveReport = true;

% 创建输出目录
if ~exist(config.outputDir, 'dir')
    mkdir(config.outputDir);
end

%% 性能监控初始化
if config.enableProfiling
    tic;
    memoryStart = memory;
    fprintf('=== 优化版多标注处理程序 ===\n');
    fprintf('性能监控已启用\n');
    fprintf('初始内存使用: %.2f MB\n', memoryStart.MemUsedMATLAB/1024/1024);
    fprintf('数据加载模式: %s\n', config.dataLoadMode);
end

%% 加载标注文件
fprintf('\n正在加载标注文件: %s\n', config.labelFile);

% 检查标注文件是否存在
if ~exist(config.labelFile, 'file')
    error('标注文件不存在: %s', config.labelFile);
end

loadStart = tic;
try
    load(config.labelFile, 'ls');
    fprintf('✓ 标注文件加载成功 (%.3f秒)\n', toc(loadStart));
catch ME
    error('标注文件加载失败: %s', ME.message);
end

% 验证ls对象
if ~exist('ls', 'var')
    error('标注文件中不存在ls变量');
end

if ~isa(ls, 'labeledSignalSet')
    error('ls变量不是labeledSignalSet对象，而是: %s', class(ls));
end

%% 获取标注信息和信号源
labels = ls.Labels;
sources = ls.Source;

fprintf('找到 %d 个标注信号\n', height(labels));

% 验证数据完整性
if height(labels) ~= length(sources)
    error('标注表和信号源数量不匹配: Labels=%d, Sources=%d', height(labels), length(sources));
end

%% 初始化统计变量
stats = struct();
stats.totalExtractedFiles = 0;
stats.totalLabels = 0;
stats.processedSignals = 0;
stats.skippedSignals = 0;
stats.processingTimes = [];
stats.memoryUsage = [];

%% 提取标注数据的主循环
fprintf('\n=== 开始处理标注数据 ===\n');

for i = 1:height(labels)
    signalStart = tic;
    
    % 获取当前行的信号名称和标注表
    signalName = labels.Row{i};
    bsTable = labels{i, 'BS'}{1};

    % 检查是否有标注数据
    if isempty(bsTable) || height(bsTable) == 0
        stats.skippedSignals = stats.skippedSignals + 1;
        if config.showProgress
            fprintf('[%d/%d] 跳过信号 %s：无标注数据\n', i, height(labels), signalName);
        end
        continue;
    end

    stats.processedSignals = stats.processedSignals + 1;
    
    if config.showProgress
        fprintf('[%d/%d] 处理信号: %s (%d个标注)\n', ...
            i, height(labels), signalName, height(bsTable));
    end

    % 解析信号名称以获取数据集、段号和通道信息
    try
        [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName);
    catch ME
        warning('无法解析信号名称 %s: %s', signalName, ME.message);
        continue;
    end

    % 根据配置的数据加载模式获取信号数据
    signalData = [];
    dataLoadStart = tic;
    
    if strcmp(config.dataLoadMode, 'embedded')
        % 模式1：从labeledSignalSet中直接提取信号数据（推荐）
        try
            signalData = sources{i};
            
            % 验证信号数据的有效性
            if isempty(signalData) || height(signalData) == 0
                warning('labeledSignalSet中的信号数据为空: %s', signalName);
                continue;
            end
            
            if config.showProgress
                fprintf('  ✓ 从labeledSignalSet提取信号 (%.3f秒)\n', toc(dataLoadStart));
            end
            
        catch ME
            warning('从labeledSignalSet提取信号失败: %s, 错误: %s', signalName, ME.message);
            continue;
        end
        
    else
        % 模式2：从外部文件加载信号数据（兼容模式）
        rawFileName = sprintf('%s_seg%03d_tt.mat', datasetInfo, segmentNum);
        rawFilePath = fullfile(config.rawDataDir, rawFileName);

        if ~exist(rawFilePath, 'file')
            warning('原始数据文件不存在: %s', rawFilePath);
            continue;
        end

        try
            rawData = load(rawFilePath);
            
            if ~isfield(rawData, signalName)
                warning('原始数据文件 %s 中不包含变量 %s', rawFilePath, signalName);
                fprintf('    可用变量: %s\n', strjoin(fieldnames(rawData), ', '));
                continue;
            end

            signalData = rawData.(signalName);
            
            if config.showProgress
                fprintf('  ✓ 从外部文件加载信号 (%.3f秒)\n', toc(dataLoadStart));
            end
            
        catch ME
            warning('从外部文件加载信号失败: %s, 错误: %s', rawFilePath, ME.message);
            continue;
        end
    end

    % 处理每个标注
    extractionStart = tic;
    for j = 1:height(bsTable)
        stats.totalLabels = stats.totalLabels + 1;

        try
            % 获取标注信息
            roiLimits = bsTable.ROILimits(j, :);
            labelValue = bsTable.Value(j);
            if iscell(labelValue)
                labelValue = labelValue{1};
            end

            % 计算还原后的真实时间
            segmentOffset = (segmentNum - 1) * 60;
            realStartTime = roiLimits(1) + segmentOffset;
            realEndTime = roiLimits(2) + segmentOffset;

            if config.showProgress
                fprintf('    标注 %d/%d: %.3f-%.3f秒 → %.3f-%.3f秒, 标签: %s\n', ...
                    j, height(bsTable), roiLimits(1), roiLimits(2), ...
                    realStartTime, realEndTime, labelValue);
            end

            % 提取信号片段
            extractedSignal = signalData(timerange(seconds(roiLimits(1)), seconds(roiLimits(2))), :);

            % 创建还原时间的时间表
            originalTime = extractedSignal.Time;
            restoredTime = originalTime + seconds(segmentOffset);

            % 创建包含还原时间的新时间表
            restoredSignal = extractedSignal;
            restoredSignal.Time = restoredTime;

            % 准备保存的数据结构
            extractedData = struct();
            extractedData.originalSignal = extractedSignal;
            extractedData.restoredSignal = restoredSignal;
            extractedData.labelInfo = struct();
            extractedData.labelInfo.value = labelValue;
            extractedData.labelInfo.originalTimeRange = roiLimits;
            extractedData.labelInfo.restoredTimeRange = [realStartTime, realEndTime];
            extractedData.labelInfo.sourceFile = signalName;
            extractedData.labelInfo.segmentNumber = segmentNum;
            extractedData.labelInfo.channel = channelInfo;
            extractedData.labelInfo.dataLoadMode = config.dataLoadMode;
            extractedData.labelInfo.processingTime = datetime('now');

            % 构造输出文件名
            outputFileName = sprintf('%s_seg%03d_%s_%s_%03d_opt.mat', ...
                datasetInfo, segmentNum, channelInfo, labelValue, j);
            outputFilePath = fullfile(config.outputDir, outputFileName);

            % 保存数据
            save(outputFilePath, 'extractedData');
            stats.totalExtractedFiles = stats.totalExtractedFiles + 1;

            if config.showProgress
                fprintf('      ✓ 已保存: %s\n', outputFileName);
            end

        catch ME
            warning('提取标注 %d 时出错: %s', j, ME.message);
        end
    end
    
    % 记录处理统计
    signalProcessTime = toc(signalStart);
    stats.processingTimes(end+1) = signalProcessTime;
    
    if config.enableProfiling
        currentMemory = memory;
        stats.memoryUsage(end+1) = currentMemory.MemUsedMATLAB/1024/1024;
    end
end

%% 生成处理报告和性能统计
fprintf('\n=== 处理完成 ===\n');
fprintf('处理的信号数量: %d\n', stats.processedSignals);
fprintf('跳过的信号数量: %d\n', stats.skippedSignals);
fprintf('总标注数量: %d\n', stats.totalLabels);
fprintf('成功提取: %d\n', stats.totalExtractedFiles);
fprintf('成功率: %.1f%%\n', (stats.totalExtractedFiles/max(stats.totalLabels,1))*100);
fprintf('输出目录: %s\n', config.outputDir);

if config.enableProfiling
    totalTime = toc;
    memoryEnd = memory;
    
    fprintf('\n=== 性能统计 ===\n');
    fprintf('总处理时间: %.3f 秒\n', totalTime);
    if ~isempty(stats.processingTimes)
        fprintf('平均每信号处理时间: %.3f 秒\n', mean(stats.processingTimes));
    end
    fprintf('最终内存使用: %.2f MB\n', memoryEnd.MemUsedMATLAB/1024/1024);
    fprintf('内存增长: %.2f MB\n', (memoryEnd.MemUsedMATLAB - memoryStart.MemUsedMATLAB)/1024/1024);
    
    if stats.totalExtractedFiles > 0
        fprintf('处理效率: %.1f 标注/秒\n', stats.totalExtractedFiles/totalTime);
    end
end

%% 保存处理报告
if config.saveReport
    reportData = struct();
    reportData.processTime = datetime('now');
    reportData.config = config;
    reportData.stats = stats;
    reportData.sourceFile = config.labelFile;
    reportData.outputDir = config.outputDir;
    
    if config.enableProfiling
        reportData.performance = struct();
        reportData.performance.totalTime = totalTime;
        reportData.performance.avgSignalTime = mean(stats.processingTimes);
        reportData.performance.memoryUsage = stats.memoryUsage;
        reportData.performance.memoryGrowth = (memoryEnd.MemUsedMATLAB - memoryStart.MemUsedMATLAB)/1024/1024;
    end
    
    reportFilePath = fullfile(config.outputDir, 'extraction_report_optimized.mat');
    save(reportFilePath, 'reportData');
    fprintf('\n优化版处理报告已保存: %s\n', reportFilePath);
end

%% 辅助函数：解析信号名称
function [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName)
%PARSESIGNALNAME 解析肠鸣音信号文件名并提取关键信息
%   从标准化的信号文件名中提取数据集标识、段号和通道信息，
%   支持肠鸣音信号分析系统的文件命名规范。

    % 使用正则表达式解析标准文件名格式
    pattern = '(data\d+_5min)_seg(\d+)_(tt\d+)';
    tokens = regexp(signalName, pattern, 'tokens');

    if isempty(tokens)
        error('PARSESIGNALNAME:InvalidFormat', ...
              '无法解析信号名称: %s\n期望格式: data{N}_5min_seg{XXX}_{channel}', signalName);
    end

    % 提取解析结果
    datasetInfo = tokens{1}{1};
    segmentNum = str2double(tokens{1}{2});
    channelInfo = tokens{1}{3};
end
