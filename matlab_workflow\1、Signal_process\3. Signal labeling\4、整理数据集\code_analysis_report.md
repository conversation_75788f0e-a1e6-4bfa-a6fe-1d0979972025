# 整理数据集 - 代码分析与使用报告

## 项目概述

本模块用于从标注文件中批量提取肠鸣音信号片段，支持时间轴还原和完整的数据结构输出。该项目成功解决了原始脚本与当前数据结构不匹配的问题，实现了高效的批量数据处理功能。

## ✅ 项目状态

**状态**: 已完成并测试通过  
**测试结果**: 成功提取2个标注数据片段  
**输出文件**: 
- `data3_5min_seg002_tt1_MB_001.mat`
- `data3_5min_seg004_tt1_SB_001.mat`

## 文件夹结构

```
4、整理数据集/
├── 1、Raw data/              # 原始分段信号数据
│   ├── data1_5min_seg001_tt.mat
│   ├── data1_5min_seg002_tt.mat
│   └── ...
├── 2、Processed data/        # 输出：提取的信号片段
├── 3、Backup/               # 备份文件夹
├── 4、Label/                # 标注文件
│   └── ls_data3.mat         # labeledSignalSet对象
├── multi_label_process.m    # 主处理脚本
├── test_multi_label_process.m # 环境检查脚本
├── verify_results.m         # 结果验证脚本
├── check_label_file.m       # 标注文件检查脚本
└── code_analysis_report.md  # 本文件
```

## 快速开始

### 1. 环境检查
```matlab
% 运行测试脚本检查环境
test_multi_label_process
```

### 2. 批量处理
```matlab
% 运行主处理脚本
multi_label_process
```

### 3. 结果验证
```matlab
% 验证处理结果
verify_results
```

## 核心问题分析与解决

### 原始问题识别

#### 1. 文件夹结构不匹配
- **原始脚本**: 扫描当前目录下所有 `label*.mat` 文件
- **实际结构**: 标注文件位于 `4、Label/ls_data3.mat`

#### 2. 标注文件格式差异
- **原始脚本**: 假设多个独立的标注文件
- **实际格式**: 单个 `labeledSignalSet` 对象，包含所有标注信息

#### 3. 数据访问方式不同
- **原始脚本**: 假设原始数据文件包含 `tt1` 变量
- **实际结构**: 原始数据文件中的变量名就是完整的信号名称（如 `data3_5min_seg002_tt1`）

#### 4. 输出组织方式
- **原始脚本**: 简单的文件名拼接
- **改进需求**: 需要时间轴还原和更完整的数据结构

### 关键修改内容

#### 1. 文件路径配置
```matlab
% 修改前：扫描当前目录
labelFiles = dir('label*.mat');

% 修改后：指定具体路径
labelFile = fullfile('4、Label', 'ls_data3.mat');
rawDataDir = '1、Raw data';
outputDir = '2、Processed data';
```

#### 2. 标注文件处理
```matlab
% 修改前：遍历多个标注文件
for fileIndex = 1:totalLabelFiles
    load(currentLabelFile, 'ls');
    
% 修改后：处理单个labeledSignalSet对象
load(labelFile, 'ls');
labels = ls.Labels;
for i = 1:height(labels)
    signalName = labels.Row{i};
    bsTable = labels{i, 'BS'}{1};
```

#### 3. 原始数据文件访问（关键修改）
```matlab
% 修改前：假设数据文件包含tt1变量
load(currentFilePath, 'tt1');

% 修改后：使用完整信号名称作为变量名
[datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName);
rawFileName = sprintf('%s_seg%03d_tt.mat', datasetInfo, segmentNum);
rawFilePath = fullfile(rawDataDir, rawFileName);
rawData = load(rawFilePath);
signalData = rawData.(signalName);  % 关键修改：使用信号名称作为变量名
```

#### 4. 时间轴还原功能
```matlab
% 新增：计算还原后的真实时间
segmentOffset = (segmentNum - 1) * 60; % 每段60秒
realStartTime = roiLimits(1) + segmentOffset;
realEndTime = roiLimits(2) + segmentOffset;

% 新增：创建还原时间的时间表
originalTime = extractedSignal.Time;
restoredTime = originalTime + seconds(segmentOffset);
restoredSignal = extractedSignal;
restoredSignal.Time = restoredTime;
```

## 输出数据格式

### 文件命名规则
```
{dataset}_seg{XXX}_{channel}_{label}_{index}.mat
```
例如：`data3_seg002_tt1_MB_001.mat`

### 数据结构
每个输出文件包含 `extractedData` 结构体：
```matlab
extractedData.originalSignal    % 原始时间轴的信号数据
extractedData.restoredSignal    % 还原时间轴的信号数据
extractedData.labelInfo         % 标注信息
  .value                        % 标签值（如SB, MB, CRS）
  .originalTimeRange            % 原始时间范围
  .restoredTimeRange            % 还原时间范围
  .sourceFile                   % 源信号文件名
  .segmentNumber                % 段号
  .channel                      % 通道信息
```

## 时间轴还原算法

### 原理
原始分段文件的时间轴都从0开始，还原算法：
```
还原时间 = 原始时间 + (段号 - 1) × 60秒
```

### 示例
- seg001: [0,60]秒 → [0,60]秒（无偏移）
- seg002: [0,60]秒 → [60,120]秒（偏移60秒）
- seg003: [0,60]秒 → [120,180]秒（偏移120秒）

## 新增功能特性

### 1. parseSignalName 函数
- 解析标准化的信号文件名
- 提取数据集标识、段号和通道信息
- 支持正则表达式模式匹配

### 2. 处理报告生成
- 详细的处理统计信息
- 自动保存处理报告到输出目录
- 包含时间戳和处理参数

### 3. 全面的错误处理
```matlab
% 文件存在性检查
if ~exist(labelFile, 'file')
    error('标注文件不存在: %s', labelFile);
end

% 对象类型验证
if ~isa(ls, 'labeledSignalSet')
    error('ls变量不是labeledSignalSet对象，而是: %s', class(ls));
end

% 原始数据文件检查
if ~exist(rawFilePath, 'file')
    warning('原始数据文件不存在: %s', rawFilePath);
    continue;
end
```

## 测试验证体系

创建了完整的测试脚本用于验证：
1. **test_multi_label_process**: 文件夹结构检查、标注文件格式验证、信号名称解析测试
2. **verify_results**: 输出结果验证、数据结构完整性检查、时间轴还原验证
3. **check_label_file**: 标注文件详细结构检查

## 故障排除

### 常见问题

1. **标注文件不存在**
   ```
   错误：标注文件不存在: 4、Label/ls_data3.mat
   解决：确保标注文件存在且路径正确
   ```

2. **原始数据文件不存在**
   ```
   警告：原始数据文件不存在: 1、Raw data/data3_seg002_tt.mat
   解决：检查原始数据文件是否完整
   ```

3. **信号名称解析失败**
   ```
   错误：无法解析信号名称: xxx
   解决：确保信号名称符合格式 data{N}_5min_seg{XXX}_tt{N}
   ```

### 调试步骤

1. 运行 `check_label_file` 检查标注文件结构
2. 运行 `test_multi_label_process` 进行全面测试
3. 检查MATLAB命令窗口的详细错误信息

## 系统要求

- MATLAB R2018b 或更高版本
- Signal Processing Toolbox
- 足够的磁盘空间（建议至少1GB）

## 性能优化

### 内存管理
- 避免重复加载相同的原始文件
- 及时释放不需要的变量
- 使用结构化的数据访问

### 错误恢复
- 单个文件处理失败不影响整体流程
- 详细的错误信息和警告提示
- 跳过无效数据继续处理

## 后续建议

1. **参数化配置**: 将硬编码的路径和参数提取为配置文件
2. **并行处理**: 对于大量数据，考虑使用并行计算
3. **数据验证**: 增加更多的数据完整性检查
4. **可视化**: 添加处理结果的可视化功能
5. **日志记录**: 实现更详细的日志记录系统

## 总结

修改后的脚本完全适配了当前文件夹的数据结构，成功解决了原始脚本与实际数据格式不匹配的问题。关键突破是发现原始数据文件中的变量名就是完整的信号名称，而不是预期的 `tt1`。项目增加了时间轴还原功能，提供了更完整的数据输出格式，并包含了全面的错误处理和验证机制。

**使用流程**: 环境检查 → 批量处理 → 结果验证，三个步骤确保数据处理的可靠性和准确性。
