%% 改进的肠鸣音信号标注数据提取程序
%LABEL_PROCESS_2 从标注文件中提取肠鸣音信号片段并进行时间轴还原
%   本程序是肠鸣音信号分析系统的核心数据提取模块，负责根据标注信息
%   从原始分段信号中提取特定时间范围的信号片段，并实现时间轴还原。
%
%   主要功能：
%   1. 解析labeledSignalSet数据结构中的标注信息
%   2. 从原始分段信号文件中提取对应时间范围的信号
%   3. 实现分段时间到连续时间的还原转换
%   4. 支持多通道信号处理（tt1和tt2通道）
%   5. 按标签类型和时间信息组织输出文件
%
%   算法核心：
%   - 时间轴还原：segment_time + (segment_number - 1) * 60秒
%   - 信号提取：基于ROILimits时间范围进行精确截取
%   - 数据结构：保持原始和还原两套时间信息
%
%   输入要求：
%   - 4、Label/ls_data3.mat: labeledSignalSet对象，包含标注信息
%   - 1、Raw data/*.mat: 原始分段信号文件（每段60秒）
%
%   输出结果：
%   - 2、Processed data/*.mat: 提取的信号片段文件
%   - extraction_report.mat: 处理统计报告
%
%   文件命名规则：
%   {dataset}_{segment}_{channel}_{label}_{index}.mat
%   例如：data3_seg002_tt1_MB_001.mat
%
%   时间轴还原规则：
%   - seg001: [0,60]秒 → [0,60]秒（无偏移）
%   - seg002: [0,60]秒 → [60,120]秒（偏移60秒）
%   - seg003: [0,60]秒 → [120,180]秒（偏移120秒）
%   - 通用公式：还原时间 = 原始时间 + (段号-1) × 60
%
%   数据结构说明：
%   extractedData.originalSignal  - 原始时间轴的信号数据
%   extractedData.restoredSignal  - 还原时间轴的信号数据
%   extractedData.labelInfo       - 标注信息（标签值、时间范围等）
%
%   使用示例：
%   运行此脚本将自动处理所有标注数据并生成提取文件
%   建议先运行test_extraction_1.m进行预检查
%
%   性能优化：
%   - 批量处理减少文件I/O开销
%   - 内存高效的信号片段提取
%   - 详细的进度报告和错误处理
%
%   作者：肠鸣音信号分析团队
%   日期：2025年
%   版本：2.0
%
%   See also: TEST_EXTRACTION_1, VALIDATE_EXTRACTION_3, DEMO_USAGE_4

clear;
clc;
close all;

%% 配置路径
labelFile = '4、Label/ls_data3.mat';
rawDataDir = '1、Raw data';
outputDir = '2、Processed data';

% 创建输出目录
if ~exist(outputDir, 'dir')
    mkdir(outputDir);
end

%% 加载标注文件
fprintf('正在加载标注文件: %s\n', labelFile);
load(labelFile);

% 获取标注信息
labels = ls.Labels;
fprintf('找到 %d 个标注文件\n', height(labels));

%% 提取标注数据的主循环
extractedCount = 0;
totalLabels = 0;

for i = 1:height(labels)
    % 获取当前行的文件名和标注表
    signalName = labels.Row{i};  % 例如：'data3_5min_seg002_tt1'
    bsTable = labels{i, 'BS'}{1}; % 获取标注表

    % 检查是否有标注数据
    if isempty(bsTable) || height(bsTable) == 0
        continue;
    end

    fprintf('\n处理信号: %s\n', signalName);
    fprintf('找到 %d 个标注\n', height(bsTable));

    % 解析文件名以获取数据集、段号和通道信息
    [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName);

    % 构造对应的原始数据文件路径
    rawFileName = sprintf('%s_seg%03d_tt.mat', datasetInfo, segmentNum);
    rawFilePath = fullfile(rawDataDir, rawFileName);

    if ~exist(rawFilePath, 'file')
        warning('原始数据文件不存在: %s', rawFilePath);
        continue;
    end

    % 加载原始数据
    fprintf('加载原始数据: %s\n', rawFileName);
    rawData = load(rawFilePath);

    % 获取对应通道的数据
    % 标注文件中的signalName就是变量名，例如：'data3_5min_seg002_tt1'
    if ~isfield(rawData, signalName)
        warning('信号变量不存在: %s', signalName);
        continue;
    end

    signalData = rawData.(signalName);

    % 处理每个标注
    for j = 1:height(bsTable)
        totalLabels = totalLabels + 1;

        % 获取标注信息
        roiLimits = bsTable.ROILimits(j, :);
        labelValue = bsTable.Value(j);
        if iscell(labelValue)
            labelValue = labelValue{1};
        end

        % 计算还原后的真实时间
        segmentOffset = (segmentNum - 1) * 60; % 每段60秒
        realStartTime = roiLimits(1) + segmentOffset;
        realEndTime = roiLimits(2) + segmentOffset;

        fprintf('  标注 %d: %.3f-%.3f秒 (原始) → %.3f-%.3f秒 (还原), 标签: %s\n', ...
            j, roiLimits(1), roiLimits(2), realStartTime, realEndTime, labelValue);

        % 提取信号片段
        try
            extractedSignal = signalData(timerange(seconds(roiLimits(1)), seconds(roiLimits(2))), :);

            % 创建还原时间的时间表
            originalTime = extractedSignal.Time;
            restoredTime = originalTime + seconds(segmentOffset);

            % 创建包含还原时间的新时间表，保持Time作为时间列名
            restoredSignal = extractedSignal;  % 复制原始结构
            restoredSignal.Time = restoredTime;  % 更新时间列

            % 准备保存的数据结构
            extractedData = struct();
            extractedData.originalSignal = extractedSignal;      % 原始时间的信号
            extractedData.restoredSignal = restoredSignal;       % 还原时间的信号
            extractedData.labelInfo = struct();
            extractedData.labelInfo.value = labelValue;
            extractedData.labelInfo.originalTimeRange = roiLimits;
            extractedData.labelInfo.restoredTimeRange = [realStartTime, realEndTime];
            extractedData.labelInfo.sourceFile = signalName;
            extractedData.labelInfo.segmentNumber = segmentNum;
            extractedData.labelInfo.channel = channelInfo;

            % 构造输出文件名
            outputFileName = sprintf('%s_seg%03d_%s_%s_%03d.mat', ...
                datasetInfo, segmentNum, channelInfo, labelValue, j);
            outputFilePath = fullfile(outputDir, outputFileName);

            % 保存数据
            save(outputFilePath, 'extractedData');
            extractedCount = extractedCount + 1;

            fprintf('    已保存: %s\n', outputFileName);

        catch ME
            warning('SIGNAL_EXTRACTION:Error', '提取信号时出错: %s', ME.message);
        end
    end
end

%% 生成处理报告
fprintf('\n=== 处理完成 ===\n');
fprintf('总标注数量: %d\n', totalLabels);
fprintf('成功提取: %d\n', extractedCount);
fprintf('输出目录: %s\n', outputDir);

% 保存处理报告
reportData = struct();
reportData.processTime = datetime('now');
reportData.totalLabels = totalLabels;
reportData.extractedCount = extractedCount;
reportData.sourceFile = labelFile;
reportData.outputDir = outputDir;

% save(fullfile(outputDir, 'extraction_report.mat'), 'reportData');
% fprintf('处理报告已保存: %s\n', fullfile(outputDir, 'extraction_report.mat'));

%% 辅助函数：解析信号名称
function [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName)
%PARSESIGNALNAME 解析肠鸣音信号文件名并提取关键信息
%   从标准化的信号文件名中提取数据集标识、段号和通道信息，
%   支持肠鸣音信号分析系统的文件命名规范。
%
%   Syntax:
%   [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName)
%
%   Inputs:
%   signalName - 信号文件名字符串 (char/string)
%                格式：'data{N}_5min_seg{XXX}_{channel}'
%                例如：'data3_5min_seg002_tt1'
%
%   Outputs:
%   datasetInfo - 数据集标识字符串 (char)
%                 例如：'data3_5min'
%   segmentNum  - 段号数值 (double)
%                 例如：2 (从seg002中提取)
%   channelInfo - 通道标识字符串 (char)
%                 例如：'tt1'
%
%   Algorithm:
%   使用正则表达式模式匹配：'(data\d+_5min)_seg(\d+)_(tt\d+)'
%   - 第一组：数据集标识（data数字_5min）
%   - 第二组：段号（数字部分）
%   - 第三组：通道标识（tt数字）
%
%   Example:
%   [dataset, segNum, channel] = parseSignalName('data3_5min_seg002_tt1');
%   % 返回：dataset = 'data3_5min', segNum = 2, channel = 'tt1'
%
%   Error Handling:
%   如果输入格式不符合预期模式，将抛出错误信息
%
%   See also: LABEL_PROCESS_2, REGEXP

    % 使用正则表达式解析标准文件名格式
    pattern = '(data\d+_5min)_seg(\d+)_(tt\d+)';
    tokens = regexp(signalName, pattern, 'tokens');

    if isempty(tokens)
        error('PARSESIGNALNAME:InvalidFormat', ...
              '无法解析信号名称: %s\n期望格式: data{N}_5min_seg{XXX}_{channel}', signalName);
    end

    % 提取解析结果
    datasetInfo = tokens{1}{1};
    segmentNum = str2double(tokens{1}{2});
    channelInfo = tokens{1}{3};
end



