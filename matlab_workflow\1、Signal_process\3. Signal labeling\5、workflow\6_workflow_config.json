{"workflow_info": {"name": "肠鸣音信号标注工作流程", "version": "1.0", "description": "自动化的5阶段肠鸣音信号处理和标注流程", "created_date": "2024-08-22", "author": "Augment Agent"}, "global_settings": {"sampling_rate": 2570, "segment_length_seconds": 60, "backup_enabled": true, "auto_file_copy": true, "cleanup_temp_files": true, "progress_display": true, "detailed_logging": true, "parallel_processing": false}, "file_formats": {"supported_audio": [".wav", ".mp3", ".m4a", ".flac"], "supported_data": [".mat"], "output_format": "mat", "compression_enabled": false}, "stage_settings": {"stage_0_training": {"name": "算法训练与开发", "enabled": true, "main_script": "Label_test.m", "preprocessing_script": "audio_preprocessing.m", "test_script": "test_preprocessing.m", "algorithm_params": {"sb_alpha": 0.08, "crs_alpha": 0.05, "sb_duration_min_ms": 8, "sb_duration_max_ms": 30, "mb_duration_min_ms": 40, "mb_duration_max_ms": 1500, "crs_duration_min_ms": 50, "crs_duration_max_ms": 3000, "density_filter_window_sec": 1, "density_filter_max_events": 5}, "input_folders": ["1、Raw data"], "output_folders": ["2、Processed data"], "backup_folders": ["3、Backup"]}, "stage_1_labeling": {"name": "信号标注器APP标注", "enabled": true, "main_script": "BowelSoundLabeler.m", "label_script": "Label_work.m", "read_script": "Read_the_tt_file.m", "gui_settings": {"auto_rename": true, "folder_memory": true, "batch_processing": true, "file_validation": true}, "naming_rules": {"no_bowel_sound": "_no", "has_bowel_sound": "_yes_N"}, "input_folders": ["1、Raw data"], "output_folders": ["2、Processed data"], "backup_folders": ["3、Backup"]}, "stage_2_dataset": {"name": "标注得到数据集", "enabled": true, "main_script": "label_process_2.m", "test_script": "test_extraction_1.m", "validate_script": "validate_extraction_3.m", "demo_script": "demo_usage_4.m", "processing_params": {"time_restoration": true, "multi_channel_support": true, "batch_extraction": true, "quality_check": true}, "input_folders": ["1、Raw data", "4、Label"], "output_folders": ["2、Processed data"], "backup_folders": ["3、Backup"]}, "stage_3_optimization": {"name": "标注结果返回标注器", "enabled": true, "main_script": "label_to_label.m", "check_script": "check_label_file.m", "optimization_params": {"quality_threshold": 0.8, "consistency_check": true, "auto_feedback": true, "iterative_improvement": true}, "input_folders": ["1、Raw data", "2、Processed data", "4、Label"], "output_folders": ["2、Processed data"], "backup_folders": ["3、Backup"]}, "stage_4_batch": {"name": "整理数据集", "enabled": true, "main_script": "multi_label_process_2.m", "test_script": "test_multi_label_process_1.m", "verify_script": "verify_results_3.m", "batch_params": {"parallel_processing": false, "memory_optimization": true, "progress_reporting": true, "error_recovery": true}, "input_folders": ["1、Raw data", "4、Label"], "output_folders": ["2、Processed data"], "backup_folders": ["3、Backup"]}}, "file_paths": {"base_directory": "../", "stage_directories": ["../0、训练标记", "../1、信号标注器APP标注", "../2、标注得到数据集", "../3、标注结果返回标注器_继续或修改标注", "../4、整理数据集"], "common_subdirs": {"raw_data": "1、Raw data", "processed_data": "2、Processed data", "backup": "3、Backup", "label": "4、Label"}}, "quality_control": {"checkpoints": [{"stage": 0, "name": "预处理质量检查", "checks": ["file_integrity", "signal_quality", "format_validation"]}, {"stage": 1, "name": "标注一致性检查", "checks": ["labeling_consistency", "file_naming", "completeness"]}, {"stage": 2, "name": "数据提取完整性检查", "checks": ["extraction_accuracy", "time_alignment", "data_integrity"]}, {"stage": 3, "name": "标注优化效果检查", "checks": ["improvement_metrics", "quality_scores", "consistency_improvement"]}, {"stage": 4, "name": "最终数据集质量检查", "checks": ["dataset_completeness", "format_consistency", "statistical_validation"]}], "error_handling": {"auto_retry": true, "max_retries": 3, "backup_on_error": true, "continue_on_warning": true}}, "logging": {"log_level": "INFO", "log_file": "workflow_log.txt", "timestamp_format": "yyyy-MM-dd HH:mm:ss", "include_stack_trace": true, "max_log_size_mb": 10, "log_rotation": true}, "performance": {"memory_limit_gb": 4, "max_parallel_workers": 4, "chunk_size": 1000, "cache_enabled": true, "temp_cleanup_interval": 3600}, "user_interface": {"show_progress_bar": true, "show_stage_details": true, "confirm_before_stage": false, "auto_open_results": false, "notification_sound": false}}