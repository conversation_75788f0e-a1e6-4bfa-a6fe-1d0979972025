%% E_PERFORMANCE_COMPARISON 性能对比测试脚本
%   对比原始版本和优化版本的C_multi_label_process脚本性能
%   测试embedded模式和external模式的性能差异
%
%   测试指标：
%   - 处理时间
%   - 内存使用
%   - 文件I/O次数
%   - 数据一致性验证
%
%   作者：肠鸣音信号分析团队
%   日期：2025年
%   版本：1.0

clear;
clc;
close all;

fprintf('=== C_multi_label_process 性能对比测试 ===\n\n');

%% 测试配置
testConfig = struct();
testConfig.labelFile = fullfile('4、Label', 'ls_data3.mat');
testConfig.rawDataDir = '1、Raw data';
testConfig.outputDir = '2、Processed data';
testConfig.numIterations = 3; % 重复测试次数

%% 清理输出目录
if exist(testConfig.outputDir, 'dir')
    % 备份现有文件
    backupDir = sprintf('%s_backup_%s', testConfig.outputDir, datestr(now, 'yyyymmdd_HHMMSS'));
    if ~isempty(dir(fullfile(testConfig.outputDir, '*.mat')))
        fprintf('备份现有输出文件到: %s\n', backupDir);
        movefile(testConfig.outputDir, backupDir);
        mkdir(testConfig.outputDir);
    end
else
    mkdir(testConfig.outputDir);
end

%% 加载测试数据
fprintf('加载测试数据...\n');
load(testConfig.labelFile);
labels = ls.Labels;
sources = ls.Source;

% 找到有标注的信号进行测试
testIndices = [];
for i = 1:height(labels)
    bsTable = labels{i, 'BS'}{1};
    if ~isempty(bsTable) && height(bsTable) > 0
        testIndices(end+1) = i;
    end
end

fprintf('找到 %d 个有标注的信号用于测试\n', length(testIndices));

if isempty(testIndices)
    fprintf('没有找到有标注的信号，无法进行性能测试\n');
    return;
end

%% 测试1：Embedded模式（优化版本）
fprintf('\n--- 测试1：Embedded模式（优化版本）---\n');
embeddedResults = struct();
embeddedResults.times = [];
embeddedResults.memoryUsage = [];
embeddedResults.extractedFiles = {};

for iter = 1:testConfig.numIterations
    fprintf('迭代 %d/%d...\n', iter, testConfig.numIterations);
    
    % 清理输出目录
    delete(fullfile(testConfig.outputDir, '*_opt.mat'));
    
    memStart = memory;
    tic;
    
    % 模拟优化版本的核心逻辑
    extractedCount = 0;
    for idx = testIndices
        signalName = labels.Row{idx};
        bsTable = labels{idx, 'BS'}{1};
        
        % 解析信号名称
        [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName);
        
        % 从labeledSignalSet直接提取
        signalData = sources{idx};
        
        % 处理每个标注
        for j = 1:height(bsTable)
            roiLimits = bsTable.ROILimits(j, :);
            labelValue = bsTable.Value(j);
            if iscell(labelValue)
                labelValue = labelValue{1};
            end
            
            % 提取信号片段
            extractedSignal = signalData(timerange(seconds(roiLimits(1)), seconds(roiLimits(2))), :);
            
            % 计算还原时间
            segmentOffset = (segmentNum - 1) * 60;
            originalTime = extractedSignal.Time;
            restoredTime = originalTime + seconds(segmentOffset);
            restoredSignal = extractedSignal;
            restoredSignal.Time = restoredTime;
            
            % 准备数据结构
            extractedData = struct();
            extractedData.originalSignal = extractedSignal;
            extractedData.restoredSignal = restoredSignal;
            extractedData.labelInfo = struct();
            extractedData.labelInfo.value = labelValue;
            extractedData.labelInfo.originalTimeRange = roiLimits;
            extractedData.labelInfo.restoredTimeRange = [roiLimits(1) + segmentOffset, roiLimits(2) + segmentOffset];
            extractedData.labelInfo.sourceFile = signalName;
            extractedData.labelInfo.segmentNumber = segmentNum;
            extractedData.labelInfo.channel = channelInfo;
            extractedData.labelInfo.dataLoadMode = 'embedded';
            
            % 保存文件
            outputFileName = sprintf('%s_seg%03d_%s_%s_%03d_opt.mat', ...
                datasetInfo, segmentNum, channelInfo, labelValue, j);
            outputFilePath = fullfile(testConfig.outputDir, outputFileName);
            save(outputFilePath, 'extractedData');
            
            if iter == 1 % 只在第一次迭代时保存文件名用于一致性检查
                embeddedResults.extractedFiles{end+1} = outputFileName;
            end
            
            extractedCount = extractedCount + 1;
        end
    end
    
    embeddedTime = toc;
    memEnd = memory;
    
    embeddedResults.times(iter) = embeddedTime;
    embeddedResults.memoryUsage(iter) = (memEnd.MemUsedMATLAB - memStart.MemUsedMATLAB) / 1024 / 1024;
    
    fprintf('  时间: %.3f秒, 内存增长: %.2f MB, 提取数量: %d\n', ...
        embeddedTime, embeddedResults.memoryUsage(iter), extractedCount);
end

%% 测试2：External模式（原始版本）
fprintf('\n--- 测试2：External模式（原始版本）---\n');
externalResults = struct();
externalResults.times = [];
externalResults.memoryUsage = [];
externalResults.extractedFiles = {};
externalResults.fileLoads = 0;

for iter = 1:testConfig.numIterations
    fprintf('迭代 %d/%d...\n', iter, testConfig.numIterations);
    
    % 清理输出目录
    delete(fullfile(testConfig.outputDir, '*_ext.mat'));
    
    memStart = memory;
    tic;
    
    % 模拟原始版本的核心逻辑
    extractedCount = 0;
    loadedFiles = containers.Map(); % 缓存已加载的文件
    
    for idx = testIndices
        signalName = labels.Row{idx};
        bsTable = labels{idx, 'BS'}{1};
        
        % 解析信号名称
        [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName);
        
        % 从外部文件加载（带缓存优化）
        rawFileName = sprintf('%s_seg%03d_tt.mat', datasetInfo, segmentNum);
        rawFilePath = fullfile(testConfig.rawDataDir, rawFileName);
        
        if ~isKey(loadedFiles, rawFilePath)
            rawData = load(rawFilePath);
            loadedFiles(rawFilePath) = rawData;
            if iter == 1
                externalResults.fileLoads = externalResults.fileLoads + 1;
            end
        else
            rawData = loadedFiles(rawFilePath);
        end
        
        signalData = rawData.(signalName);
        
        % 处理每个标注
        for j = 1:height(bsTable)
            roiLimits = bsTable.ROILimits(j, :);
            labelValue = bsTable.Value(j);
            if iscell(labelValue)
                labelValue = labelValue{1};
            end
            
            % 提取信号片段
            extractedSignal = signalData(timerange(seconds(roiLimits(1)), seconds(roiLimits(2))), :);
            
            % 计算还原时间
            segmentOffset = (segmentNum - 1) * 60;
            originalTime = extractedSignal.Time;
            restoredTime = originalTime + seconds(segmentOffset);
            restoredSignal = extractedSignal;
            restoredSignal.Time = restoredTime;
            
            % 准备数据结构
            extractedData = struct();
            extractedData.originalSignal = extractedSignal;
            extractedData.restoredSignal = restoredSignal;
            extractedData.labelInfo = struct();
            extractedData.labelInfo.value = labelValue;
            extractedData.labelInfo.originalTimeRange = roiLimits;
            extractedData.labelInfo.restoredTimeRange = [roiLimits(1) + segmentOffset, roiLimits(2) + segmentOffset];
            extractedData.labelInfo.sourceFile = signalName;
            extractedData.labelInfo.segmentNumber = segmentNum;
            extractedData.labelInfo.channel = channelInfo;
            extractedData.labelInfo.dataLoadMode = 'external';
            
            % 保存文件
            outputFileName = sprintf('%s_seg%03d_%s_%s_%03d_ext.mat', ...
                datasetInfo, segmentNum, channelInfo, labelValue, j);
            outputFilePath = fullfile(testConfig.outputDir, outputFileName);
            save(outputFilePath, 'extractedData');
            
            if iter == 1 % 只在第一次迭代时保存文件名用于一致性检查
                externalResults.extractedFiles{end+1} = outputFileName;
            end
            
            extractedCount = extractedCount + 1;
        end
    end
    
    externalTime = toc;
    memEnd = memory;
    
    externalResults.times(iter) = externalTime;
    externalResults.memoryUsage(iter) = (memEnd.MemUsedMATLAB - memStart.MemUsedMATLAB) / 1024 / 1024;
    
    fprintf('  时间: %.3f秒, 内存增长: %.2f MB, 提取数量: %d\n', ...
        externalTime, externalResults.memoryUsage(iter), extractedCount);
end

%% 数据一致性验证
fprintf('\n--- 数据一致性验证 ---\n');
consistencyCheck = true;

if length(embeddedResults.extractedFiles) == length(externalResults.extractedFiles)
    for i = 1:length(embeddedResults.extractedFiles)
        embeddedFile = fullfile(testConfig.outputDir, embeddedResults.extractedFiles{i});
        externalFile = fullfile(testConfig.outputDir, externalResults.extractedFiles{i});
        
        if exist(embeddedFile, 'file') && exist(externalFile, 'file')
            embeddedData = load(embeddedFile);
            externalData = load(externalFile);
            
            % 检查信号数据是否相等
            if ~isequal(embeddedData.extractedData.originalSignal{:,1}, ...
                       externalData.extractedData.originalSignal{:,1})
                fprintf('❌ 数据不一致：文件 %d\n', i);
                consistencyCheck = false;
            end
        else
            fprintf('❌ 文件缺失：embedded=%s, external=%s\n', ...
                embeddedResults.extractedFiles{i}, externalResults.extractedFiles{i});
            consistencyCheck = false;
        end
    end
    
    if consistencyCheck
        fprintf('✅ 数据一致性验证通过\n');
    end
else
    fprintf('❌ 提取文件数量不匹配：embedded=%d, external=%d\n', ...
        length(embeddedResults.extractedFiles), length(externalResults.extractedFiles));
    consistencyCheck = false;
end

%% 性能统计分析
fprintf('\n=== 性能对比结果 ===\n');

% 计算平均值和标准差
embeddedAvgTime = mean(embeddedResults.times);
embeddedStdTime = std(embeddedResults.times);
embeddedAvgMem = mean(embeddedResults.memoryUsage);

externalAvgTime = mean(externalResults.times);
externalStdTime = std(externalResults.times);
externalAvgMem = mean(externalResults.memoryUsage);

% 性能提升计算
timeImprovement = ((externalAvgTime - embeddedAvgTime) / externalAvgTime) * 100;
memoryImprovement = ((externalAvgMem - embeddedAvgMem) / externalAvgMem) * 100;

fprintf('\n处理时间对比：\n');
fprintf('  Embedded模式: %.3f ± %.3f 秒\n', embeddedAvgTime, embeddedStdTime);
fprintf('  External模式: %.3f ± %.3f 秒\n', externalAvgTime, externalStdTime);
fprintf('  性能提升: %.1f%%\n', timeImprovement);

fprintf('\n内存使用对比：\n');
fprintf('  Embedded模式: %.2f MB\n', embeddedAvgMem);
fprintf('  External模式: %.2f MB\n', externalAvgMem);
fprintf('  内存节省: %.1f%%\n', memoryImprovement);

fprintf('\n文件I/O对比：\n');
fprintf('  Embedded模式: 1 次文件加载 (labeledSignalSet)\n');
fprintf('  External模式: %d 次文件加载 (原始数据文件)\n', externalResults.fileLoads);
fprintf('  I/O减少: %.1f%%\n', ((externalResults.fileLoads - 1) / externalResults.fileLoads) * 100);

%% 保存测试结果
testResults = struct();
testResults.testTime = datetime('now');
testResults.embeddedResults = embeddedResults;
testResults.externalResults = externalResults;
testResults.consistencyCheck = consistencyCheck;
testResults.performance = struct();
testResults.performance.timeImprovement = timeImprovement;
testResults.performance.memoryImprovement = memoryImprovement;
testResults.performance.ioReduction = ((externalResults.fileLoads - 1) / externalResults.fileLoads) * 100;

save(fullfile(testConfig.outputDir, 'performance_comparison_results.mat'), 'testResults');
fprintf('\n测试结果已保存: %s\n', fullfile(testConfig.outputDir, 'performance_comparison_results.mat'));

%% 推荐建议
fprintf('\n=== 推荐建议 ===\n');
if timeImprovement > 10
    fprintf('✅ 推荐使用embedded模式：显著的性能提升 (%.1f%%)\n', timeImprovement);
elseif timeImprovement > 0
    fprintf('✅ 推荐使用embedded模式：轻微的性能提升 (%.1f%%)\n', timeImprovement);
else
    fprintf('⚠️  两种模式性能相近，可根据具体需求选择\n');
end

if consistencyCheck
    fprintf('✅ 数据一致性验证通过，可安全切换到embedded模式\n');
else
    fprintf('❌ 数据一致性验证失败，建议进一步调试后再切换\n');
end

fprintf('\n测试完成！\n');

%% 辅助函数
function [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName)
    pattern = '(data\d+_5min)_seg(\d+)_(tt\d+)';
    tokens = regexp(signalName, pattern, 'tokens');
    
    if isempty(tokens)
        error('无法解析信号名称: %s', signalName);
    end
    
    datasetInfo = tokens{1}{1};
    segmentNum = str2double(tokens{1}{2});
    channelInfo = tokens{1}{3};
end
