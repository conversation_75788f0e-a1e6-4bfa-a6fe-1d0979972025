# 整理数据集 - 执行顺序说明

## 📋 文件夹功能概述

本文件夹用于批量处理多个标注文件，构建完整的训练数据集。主要功能包括批量处理`4、Label`文件夹中的所有标注文件、统一的文件命名规则、自动提取和分类不同类型的肠鸣音事件，以及生成完整的数据集统计报告。

## 🔢 脚本执行顺序

按照数字序号顺序执行，确保批量数据集构建的完整性和一致性：

### 1️⃣ 第一步：测试批量处理功能
**1_test_multi_label_process.m** - 批量处理测试
- **功能**：测试多标注文件批量处理功能是否正常
- **用途**：验证批量处理算法，确保大规模数据处理无误
- **输入**：少量测试用标注文件
- **输出**：测试结果和功能验证报告
- **建议**：批量处理前必须运行，特别是处理大量数据时

### 2️⃣ 第二步：执行批量数据集构建
**2_multi_label_process.m** - 多标注文件批量处理（核心程序）
- **功能**：批量处理所有标注文件的主程序（306行代码）
- **用途**：构建完整的训练数据集
- **输入**：
  - `4、Label/` 文件夹中的所有标注文件
  - `1、Raw data/` 文件夹中的原始分段信号文件
- **输出**：
  - `2、Processed data/` 文件夹中的最终数据集文件
  - 统一命名格式的信号片段文件
  - 完整的数据集统计报告
- **特点**：
  - 支持批量处理多个标注文件
  - 统一的文件命名规则
  - 自动提取和分类不同类型的肠鸣音事件
- **建议**：这是核心处理脚本，用于生产环境

### 3️⃣ 第三步：验证最终结果
**3_verify_results.m** - 结果验证工具
- **功能**：验证批量处理结果的完整性和正确性
- **用途**：质量控制，确保最终数据集符合要求
- **输入**：`2、Processed data/` 中的批量处理结果
- **输出**：
  - 完整性验证报告
  - 数据质量评估
  - 统计汇总信息
- **建议**：每次批量处理后都必须运行

## 📁 文件夹结构说明

```
4、整理数据集/
├── 0_执行顺序说明.md              # 本说明文件
├── 1_test_multi_label_process.m   # 批量处理测试
├── 2_multi_label_process.m        # 多标注文件批量处理 ⭐
├── 3_verify_results.m             # 结果验证工具
├── 1、Raw data/                   # 原始分段信号文件
├── 2、Processed data/             # 最终的数据集文件
├── 3、Backup/                     # 备份文件夹
├── 4、Label/                      # 标注文件存储
└── code_analysis_report.md        # 代码分析报告
```

## 🎯 推荐使用流程

### 标准批量处理流程
```matlab
% 1. 测试批量处理功能
run('1_test_multi_label_process.m')

% 2. 执行批量处理
run('2_multi_label_process.m')

% 3. 验证最终结果
run('3_verify_results.m')
```

### 生产环境处理（有经验用户）
```matlab
% 直接执行批量处理
run('2_multi_label_process.m')

% 验证结果
run('3_verify_results.m')
```

### 大数据集处理
```matlab
% 先小批量测试
run('1_test_multi_label_process.m')

% 确认无误后批量处理
run('2_multi_label_process.m')

% 详细验证
run('3_verify_results.m')
```

## ⚙️ 批量处理参数说明

### 主要处理能力
- **并行处理**：可选的并行处理支持（默认关闭）
- **内存优化**：智能内存管理，避免内存溢出
- **进度报告**：实时显示处理进度
- **错误恢复**：支持错误恢复和断点续传

### 输入要求
- **标注文件**：`4、Label/` 文件夹中的所有.mat标注文件
- **信号文件**：`1、Raw data/` 文件夹中对应的分段信号文件
- **文件匹配**：确保标注文件和信号文件名称对应

### 输出格式
- **统一命名**：`{dataset}_seg{XXX}_{channel}_{label}_{index}.mat`
- **分类存储**：按标签类型（SB、MB、CRS等）组织
- **统计报告**：包含处理统计和质量指标

## 🔧 批量处理优化

### 性能优化建议
1. **内存管理**
   - 监控内存使用情况
   - 适当调整批处理大小
   - 及时清理临时变量

2. **并行处理**
   - 根据硬件配置启用并行处理
   - 平衡并行度和内存使用
   - 监控CPU使用率

3. **存储优化**
   - 使用SSD存储提高I/O性能
   - 定期清理临时文件
   - 合理规划存储空间

### 错误处理策略
- **自动重试**：遇到临时错误自动重试
- **错误日志**：详细记录错误信息
- **断点续传**：支持从中断点继续处理
- **数据备份**：处理前自动备份重要数据

## 📊 质量控制和验证

### 验证检查点
1. **数据完整性**
   - 检查所有标注文件是否都被处理
   - 验证输出文件数量是否符合预期
   - 确认没有数据丢失

2. **格式一致性**
   - 验证文件命名格式统一
   - 检查数据结构一致性
   - 确认标签分类正确

3. **统计验证**
   - 比较处理前后的统计数据
   - 验证各类型事件的数量
   - 检查时间分布的合理性

### 性能指标
- **处理速度**：文件/分钟的处理速度
- **成功率**：成功处理的文件比例
- **错误率**：处理失败的文件比例
- **资源使用**：CPU和内存使用情况

## 🚨 故障排除

### 常见问题
1. **内存不足**
   - 减少并行处理数量
   - 分批处理大数据集
   - 增加虚拟内存

2. **文件匹配错误**
   - 检查标注文件和信号文件的命名对应关系
   - 验证文件路径设置
   - 确认文件格式正确

3. **处理中断**
   - 检查磁盘空间是否充足
   - 验证文件访问权限
   - 查看错误日志定位问题

4. **结果异常**
   - 运行测试脚本验证功能
   - 检查输入数据质量
   - 比较小批量和大批量处理结果

### 解决步骤
1. 运行`1_test_multi_label_process.m`诊断问题
2. 检查系统资源（内存、磁盘空间）
3. 验证输入文件的完整性
4. 查看详细的错误日志
5. 参考`code_analysis_report.md`了解算法细节

## 📈 最佳实践

### 处理前准备
- [ ] 确认所有输入文件完整
- [ ] 检查磁盘空间充足
- [ ] 备份重要数据
- [ ] 运行测试脚本验证功能

### 处理过程中
- [ ] 监控处理进度
- [ ] 观察系统资源使用
- [ ] 记录处理参数和设置
- [ ] 保存处理日志

### 处理完成后
- [ ] 运行验证脚本检查结果
- [ ] 生成统计报告
- [ ] 备份最终数据集
- [ ] 清理临时文件

---

**创建时间**: 2024-08-22  
**版本**: 1.0  
**说明**: 此文件提供了批量数据集构建的完整执行指导
