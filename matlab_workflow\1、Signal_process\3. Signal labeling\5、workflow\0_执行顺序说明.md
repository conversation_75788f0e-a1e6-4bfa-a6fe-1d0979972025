# 肠鸣音信号标注工作流程自动化工具 - 执行顺序说明

## 📋 文件执行顺序

按照数字序号顺序执行，确保最佳使用体验：

### 🔍 第一步：验证和准备
**1_verify_installation.m** - 验证安装和配置
- 功能：检查所有必需文件是否存在，验证基本功能
- 使用：`run('1_verify_installation.m')`
- 建议：首次使用必须运行

### 🚀 第二步：快速入门（推荐）
**2_quick_start.m** - 快速入门向导
- 功能：提供友好的菜单界面，引导用户完成各种操作
- 使用：`run('2_quick_start.m')`
- 建议：新用户推荐使用，提供向导式操作

### 📚 第三步：学习和演示
**3_workflow_demo.m** - 使用示例和演示
- 功能：展示所有工具的基本用法和功能
- 使用：`run('3_workflow_demo.m')`
- 建议：了解工具功能和使用方法

### 🧪 第四步：功能测试
**4_test_automation_tools.m** - 功能测试脚本
- 功能：测试所有自动化工具的基本功能是否正常
- 使用：`run('4_test_automation_tools.m')`
- 建议：确保工具正常工作

### ⚡ 第五步：执行工作流程
**5_workflow_controller.m** - 主控制器（核心工具）
- 功能：一键式执行整个5阶段工作流程
- 使用：`workflow_controller()` 或各种参数组合
- 建议：这是主要的执行工具

## 🛠️ 支持工具（按需使用）

### 配置管理
**6_workflow_config.json** - 配置文件
- 功能：存储所有参数设置
- 说明：通常不需要直接编辑，通过config_manager管理

**7_config_manager.m** - 配置管理器
- 功能：管理配置参数，备份和恢复配置
- 使用：`config_manager('load')` 等

### 文件管理
**8_file_manager.m** - 文件管理器
- 功能：自动管理各阶段间的文件传递
- 使用：`file_manager('copy_stage_outputs', 0, 1)` 等

## 📖 文档资料

**9_README.md** - 自动化工具说明
- 内容：工具概述和基本使用方法

**10_AUTOMATION_GUIDE.md** - 详细使用指南
- 内容：完整的使用指南和高级功能说明

## 🎯 推荐使用流程

### 首次使用者
```matlab
% 1. 验证安装
run('1_verify_installation.m')

% 2. 快速入门（推荐）
run('2_quick_start.m')
```

### 有经验用户
```matlab
% 1. 验证安装（可选）
run('1_verify_installation.m')

% 2. 直接执行工作流程
workflow_controller()

% 或预演模式
workflow_controller('dry_run', true)
```

### 开发和调试
```matlab
% 1. 功能测试
run('4_test_automation_tools.m')

% 2. 查看演示
run('3_workflow_demo.m')

% 3. 自定义执行
workflow_controller('start_stage', 2, 'interactive', true)
```

## ⚠️ 重要提示

1. **执行顺序**：建议按照数字序号顺序执行，特别是首次使用
2. **工作目录**：确保在 `5、workflow` 目录下执行这些脚本
3. **依赖关系**：5_workflow_controller.m 依赖于 6-8 号文件
4. **配置文件**：6_workflow_config.json 是核心配置，请勿随意删除
5. **日志文件**：执行过程中会生成日志文件，可用于故障排除

## 🔧 故障排除

如果遇到问题：
1. 首先运行 `1_verify_installation.m` 检查安装
2. 查看生成的日志文件
3. 参考 `10_AUTOMATION_GUIDE.md` 获取详细帮助
4. 使用 `2_quick_start.m` 的向导功能

---

**创建时间**: 2024-08-22  
**版本**: 1.0  
**说明**: 此文件提供了清晰的执行顺序指导，帮助用户正确使用自动化工具
