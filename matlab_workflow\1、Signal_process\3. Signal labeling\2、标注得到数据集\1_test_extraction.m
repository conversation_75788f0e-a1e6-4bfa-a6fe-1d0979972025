%% 测试标注数据提取功能
%TEST_EXTRACTION_1 验证标注数据提取系统的核心功能和数据完整性
%   本脚本是标注数据提取系统的测试和验证工具，用于检查数据结构、
%   解析功能和文件处理逻辑的正确性。
%
%   主要测试功能：
%   1. 标注文件加载和数据结构验证
%   2. 信号名称解析功能测试
%   3. 原始数据文件存在性检查
%   4. 标注信息提取和显示
%   5. 时间轴计算验证
%
%   测试流程：
%   - 加载labeledSignalSet对象
%   - 验证数据结构完整性
%   - 测试parseSignalName函数
%   - 检查原始数据文件可访问性
%   - 验证标注时间范围和标签值
%
%   输入要求：
%   - 4、Label/ls_data3.mat (标注文件)
%   - 1、Raw data/*.mat (原始信号数据文件)
%
%   输出结果：
%   - 详细的测试结果报告
%   - 数据结构信息显示
%   - 错误诊断信息
%
%   使用说明：
%   在运行label_process_2.m之前，建议先运行此测试脚本
%   以确保所有依赖文件和数据结构都正确
%
%   作者：肠鸣音信号分析团队
%   日期：2025年
%   版本：1.0
%
%   See also: LABEL_PROCESS_2, VALIDATE_EXTRACTION_3, DEMO_USAGE_4

clear;
clc;
close all;

%% 配置路径
labelFile = '4、Label/ls_data3.mat';
rawDataDir = '1、Raw data';
outputDir = '2、Processed data';

% 创建输出目录
if ~exist(outputDir, 'dir')
    mkdir(outputDir);
end

%% 加载标注文件
fprintf('正在加载标注文件: %s\n', labelFile);
try
    load(labelFile);
    fprintf('✓ 标注文件加载成功\n');
catch ME
    fprintf('✗ 标注文件加载失败: %s\n', ME.message);
    return;
end

%% 检查数据结构
fprintf('\n=== 数据结构检查 ===\n');
fprintf('labeledSignalSet对象信息:\n');
fprintf('  成员数量: %d\n', ls.NumMembers);
fprintf('  标签表行数: %d\n', height(ls.Labels));

% 显示所有标注文件
fprintf('\n标注文件列表:\n');
for i = 1:height(ls.Labels)
    signalName = ls.Labels.Row{i};
    bsTable = ls.Labels{i, 'BS'}{1};
    labelCount = height(bsTable);
    fprintf('  %d. %s (%d个标注)\n', i, signalName, labelCount);
end

%% 测试解析函数
fprintf('\n=== 测试信号名称解析 ===\n');
testSignalName = 'data3_5min_seg002_tt1';
try
    [datasetInfo, segmentNum, channelInfo] = parseSignalName(testSignalName);
    fprintf('✓ 解析成功: %s → 数据集:%s, 段号:%d, 通道:%s\n', ...
        testSignalName, datasetInfo, segmentNum, channelInfo);
catch ME
    fprintf('✗ 解析失败: %s\n', ME.message);
end

%% 测试单个文件处理
fprintf('\n=== 测试单个文件处理 ===\n');

% 找到第一个有标注的文件
targetSignal = '';
targetBsTable = [];
for i = 1:height(ls.Labels)
    signalName = ls.Labels.Row{i};
    bsTable = ls.Labels{i, 'BS'}{1};
    if height(bsTable) > 0
        targetSignal = signalName;
        targetBsTable = bsTable;
        break;
    end
end

if isempty(targetSignal)
    fprintf('✗ 未找到有标注的信号\n');
    return;
end

fprintf('测试信号: %s\n', targetSignal);
fprintf('标注数量: %d\n', height(targetBsTable));

% 显示标注详情
for j = 1:height(targetBsTable)
    roiLimits = targetBsTable.ROILimits(j, :);
    labelValue = targetBsTable.Value(j);  % 不使用元胞索引
    if iscell(labelValue)
        labelValue = labelValue{1};  % 如果是元胞，提取内容
    end
    fprintf('  标注 %d: %.3f-%.3f秒, 标签:%s\n', j, roiLimits(1), roiLimits(2), labelValue);
end

%% 测试原始数据加载
fprintf('\n=== 测试原始数据加载 ===\n');
[datasetInfo, segmentNum, channelInfo] = parseSignalName(targetSignal);
rawFileName = sprintf('%s_seg%03d_tt.mat', datasetInfo, segmentNum);
rawFilePath = fullfile(rawDataDir, rawFileName);

fprintf('原始数据文件: %s\n', rawFileName);

if exist(rawFilePath, 'file')
    fprintf('✓ 原始数据文件存在\n');
    
    try
        rawData = load(rawFilePath);
        fprintf('✓ 原始数据加载成功\n');
        
        % 显示变量
        fprintf('文件中的变量:\n');
        fieldNames = fieldnames(rawData);
        for k = 1:length(fieldNames)
            varName = fieldNames{k};
            varData = rawData.(varName);
            if istimetable(varData)
                fprintf('  %s: timetable, %d行, 时间范围: %.3f-%.3f秒\n', ...
                    varName, height(varData), seconds(varData.Time(1)), seconds(varData.Time(end)));
            else
                fprintf('  %s: %s\n', varName, class(varData));
            end
        end
        
        % 检查目标信号变量
        if isfield(rawData, targetSignal)
            fprintf('✓ 目标信号变量存在: %s\n', targetSignal);
            signalData = rawData.(targetSignal);
            
            % 测试时间范围提取
            fprintf('\n=== 测试时间范围提取 ===\n');
            roiLimits = targetBsTable.ROILimits(1, :);
            labelValue = targetBsTable.Value(1);
            if iscell(labelValue)
                labelValue = labelValue{1};
            end
            
            fprintf('提取时间范围: %.3f-%.3f秒\n', roiLimits(1), roiLimits(2));
            
            try
                extractedSignal = signalData(timerange(seconds(roiLimits(1)), seconds(roiLimits(2))), :);
                fprintf('✓ 信号提取成功, 长度: %d个采样点\n', height(extractedSignal));
                
                % 测试时间轴还原
                segmentOffset = (segmentNum - 1) * 60;
                realStartTime = roiLimits(1) + segmentOffset;
                realEndTime = roiLimits(2) + segmentOffset;
                
                fprintf('时间轴还原: %.3f-%.3f秒 → %.3f-%.3f秒\n', ...
                    roiLimits(1), roiLimits(2), realStartTime, realEndTime);
                
                % 创建还原时间的时间表
                originalTime = extractedSignal.Time;
                restoredTime = originalTime + seconds(segmentOffset);
                
                fprintf('✓ 时间轴还原成功\n');
                fprintf('  原始时间范围: %.3f-%.3f秒\n', ...
                    seconds(originalTime(1)), seconds(originalTime(end)));
                fprintf('  还原时间范围: %.3f-%.3f秒\n', ...
                    seconds(restoredTime(1)), seconds(restoredTime(end)));
                
            catch ME
                fprintf('✗ 信号提取失败: %s\n', ME.message);
            end
            
        else
            fprintf('✗ 目标信号变量不存在: %s\n', targetSignal);
        end
        
    catch ME
        fprintf('✗ 原始数据加载失败: %s\n', ME.message);
    end
    
else
    fprintf('✗ 原始数据文件不存在: %s\n', rawFilePath);
end

fprintf('\n=== 测试完成 ===\n');

%% 辅助函数：解析信号名称
function [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName)
    % 解析信号名称，例如：'data3_5min_seg002_tt1'
    % 返回：datasetInfo = 'data3_5min', segmentNum = 2, channelInfo = 'tt1'
    
    % 使用正则表达式解析
    pattern = '(data\d+_5min)_seg(\d+)_(tt\d+)';
    tokens = regexp(signalName, pattern, 'tokens');
    
    if isempty(tokens)
        error('无法解析信号名称: %s', signalName);
    end
    
    datasetInfo = tokens{1}{1};
    segmentNum = str2double(tokens{1}{2});
    channelInfo = tokens{1}{3};
end
