%% 提取数据使用示例
%DEMO_USAGE_4 演示如何使用提取的标注数据进行分析和可视化
%   本脚本展示了如何加载、分析和可视化从肠鸣音信号中提取的标注数据片段。
%   主要功能包括信号可视化、特征提取、统计分析和比较分析。
%
%   主要功能：
%   1. 加载处理后的标注数据文件
%   2. 展示数据结构和基本信息
%   3. 可视化信号片段（原始时间和还原时间）
%   4. 提取和比较不同标签类型的特征
%   5. 生成统计分析图表
%
%   输入要求：
%   - 2、Processed data文件夹中包含提取的.mat数据文件
%   - 数据文件应由label_process_2.m生成
%
%   输出结果：
%   - 信号可视化图表
%   - 特征比较分析图
%   - 统计分析结果
%   - 保存的分析图像文件
%
%   使用示例：
%   运行此脚本前，请确保已运行label_process_2.m生成处理后的数据
%
%   依赖文件：
%   - 2、Processed data/*.mat (由label_process_2.m生成)
%   - extraction_report.mat (处理报告文件)
%
%   作者：肠鸣音信号分析团队
%   日期：2025年
%   版本：4.0
%
%   See also: LABEL_PROCESS_2, VALIDATE_EXTRACTION_3, TEST_EXTRACTION_1

clear;
clc;
close all;

%% 配置
processedDataDir = '2、Processed data';

%% 获取所有提取的数据文件
allMatFiles = dir(fullfile(processedDataDir, '*.mat'));

% 过滤出真正的提取数据文件（以data开头的文件）
excludeFiles = {'extraction_report.mat', 'analysis_results.mat'};
isDataFile = false(size(allMatFiles));

for i = 1:length(allMatFiles)
    fileName = allMatFiles(i).name;
    % 检查是否是提取的数据文件：以data开头且不在排除列表中
    if startsWith(fileName, 'data') && ~any(strcmp(fileName, excludeFiles))
        isDataFile(i) = true;
    end
end

dataFiles = allMatFiles(isDataFile);

% 显示文件过滤信息
fprintf('目录中共有 %d 个.mat文件\n', length(allMatFiles));
if length(allMatFiles) > length(dataFiles)
    fprintf('过滤掉的文件: ');
    excludedFiles = allMatFiles(~isDataFile);
    for i = 1:length(excludedFiles)
        fprintf('%s ', excludedFiles(i).name);
    end
    fprintf('\n');
end

if isempty(dataFiles)
    fprintf('未找到提取的数据文件，请先运行 label_process_2.m\n');
    return;
end

fprintf('找到 %d 个有效的提取数据文件:\n', length(dataFiles));
for i = 1:length(dataFiles)
    fprintf('  %d. %s\n', i, dataFiles(i).name);
end

%% 示例1：加载并显示第一个数据文件的详细信息
fprintf('\n=== 示例1：数据结构展示 ===\n');
firstFile = fullfile(processedDataDir, dataFiles(1).name);

% 安全加载数据文件，包含错误检查
try
    loadedData = load(firstFile);
    if ~isfield(loadedData, 'extractedData')
        error('文件 %s 不包含 extractedData 字段', dataFiles(1).name);
    end
    extractedData = loadedData.extractedData;
catch ME
    fprintf('加载文件失败: %s\n', ME.message);
    fprintf('文件内容: ');
    disp(who('-file', firstFile));
    return;
end

fprintf('文件: %s\n', dataFiles(1).name);
fprintf('标签信息:\n');
disp(extractedData.labelInfo);

% 获取原始信号的时间
originalTimeVec = extractedData.originalSignal.Time;
fprintf('原始信号时间范围: %.3f - %.3f 秒\n', ...
    seconds(originalTimeVec(1)), seconds(originalTimeVec(end)));

% 获取还原信号的时间，使用正确的时间访问方式
restoredTimeVec = extractedData.restoredSignal.Time;
fprintf('还原信号时间范围: %.3f - %.3f 秒\n', ...
    seconds(restoredTimeVec(1)), seconds(restoredTimeVec(end)));

fprintf('信号长度: %d 个采样点\n', height(extractedData.originalSignal));

%% 示例2：信号可视化
fprintf('\n=== 示例2：信号可视化 ===\n');

% 选择前几个文件进行可视化
numToPlot = min(4, length(dataFiles));
figure('Name', '标注信号可视化', 'Position', [100, 100, 1200, 800]);

for i = 1:numToPlot
    filePath = fullfile(processedDataDir, dataFiles(i).name);

    % 安全加载数据文件
    try
        loadedData = load(filePath);
        if ~isfield(loadedData, 'extractedData')
            warning('文件 %s 不包含 extractedData 字段，跳过', dataFiles(i).name);
            continue;
        end
        extractedData = loadedData.extractedData;
    catch ME
        warning('DEMO_USAGE:LoadError', '加载文件失败: %s，跳过', ME.message);
        continue;
    end
    
    subplot(numToPlot, 1, i);
    
    % 绘制还原时间的信号
    restoredTime = extractedData.restoredSignal.Time;
    plot(seconds(restoredTime), extractedData.restoredSignal{:,1}, 'b-', 'LineWidth', 1);
    
    title(sprintf('文件: %s, 标签: %s, 段: %d', ...
        dataFiles(i).name, extractedData.labelInfo.value, ...
        extractedData.labelInfo.segmentNumber));
    
    xlabel('时间 (秒)');
    ylabel('信号幅值');
    grid on;
    
    % 添加时间范围标注
    xlim([extractedData.labelInfo.restoredTimeRange(1) - 0.5, ...
          extractedData.labelInfo.restoredTimeRange(2) + 0.5]);
end

% 保存图像
% saveas(gcf, fullfile(processedDataDir, 'signal_visualization.png'));
% fprintf('信号可视化已保存: %s\n', fullfile(processedDataDir, 'signal_visualization.png'));

%% 示例3：按标签分类分析
fprintf('\n=== 示例3：按标签分类分析 ===\n');

% 收集所有数据
allData = struct();
allData.signals = {};
allData.labels = {};
allData.durations = [];
allData.segments = [];

for i = 1:length(dataFiles)
    filePath = fullfile(processedDataDir, dataFiles(i).name);

    % 安全加载数据文件
    try
        loadedData = load(filePath);
        if ~isfield(loadedData, 'extractedData')
            warning('文件 %s 不包含 extractedData 字段，跳过', dataFiles(i).name);
            continue;
        end
        extractedData = loadedData.extractedData;
    catch ME
        warning('DEMO_USAGE:LoadError', '加载文件失败: %s，跳过', ME.message);
        continue;
    end
    
    allData.signals{end+1} = extractedData.restoredSignal{:,1};

    % 确保标签值是字符串
    labelValue = extractedData.labelInfo.value;
    if iscell(labelValue)
        labelValue = labelValue{1};
    end
    if ~ischar(labelValue) && ~isstring(labelValue)
        labelValue = char(labelValue);
    end
    allData.labels{end+1} = char(labelValue);

    allData.durations(end+1) = extractedData.labelInfo.restoredTimeRange(2) - ...
                               extractedData.labelInfo.restoredTimeRange(1);
    allData.segments(end+1) = extractedData.labelInfo.segmentNumber;
end

% 统计不同标签的特征
uniqueLabels = unique(allData.labels);
fprintf('标签类型分析:\n');

labelStats = struct();
for i = 1:length(uniqueLabels)
    label = uniqueLabels{i};
    labelIndices = strcmp(allData.labels, label);
    
    labelStats.(label) = struct();
    labelStats.(label).count = sum(labelIndices);
    labelStats.(label).avgDuration = mean(allData.durations(labelIndices));
    labelStats.(label).stdDuration = std(allData.durations(labelIndices));
    
    % 计算信号统计特征（优化版本 - 预分配内存）
    labelSignals = allData.signals(labelIndices);

    % 预计算总长度以预分配内存
    totalLength = 0;
    for j = 1:length(labelSignals)
        totalLength = totalLength + length(labelSignals{j});
    end

    % 预分配内存并高效拼接
    allAmplitudes = zeros(totalLength, 1);
    currentIdx = 1;
    for j = 1:length(labelSignals)
        signalLength = length(labelSignals{j});
        allAmplitudes(currentIdx:currentIdx+signalLength-1) = labelSignals{j};
        currentIdx = currentIdx + signalLength;
    end
    
    labelStats.(label).meanAmplitude = mean(allAmplitudes);
    labelStats.(label).stdAmplitude = std(allAmplitudes);
    labelStats.(label).maxAmplitude = max(allAmplitudes);
    labelStats.(label).minAmplitude = min(allAmplitudes);
    
    fprintf('  %s: 数量=%d, 平均持续时间=%.3f±%.3f秒, 平均幅值=%.3f±%.3f\n', ...
        label, labelStats.(label).count, ...
        labelStats.(label).avgDuration, labelStats.(label).stdDuration, ...
        labelStats.(label).meanAmplitude, labelStats.(label).stdAmplitude);
end

%% 示例4：特征对比可视化
fprintf('\n=== 示例4：特征对比可视化 ===\n');

if length(uniqueLabels) > 1
    figure('Name', '标签特征对比', 'Position', [200, 200, 1000, 600]);
    
    % 持续时间对比
    subplot(2,2,1);
    durations_by_label = {};
    group_labels = {};
    for i = 1:length(uniqueLabels)
        label = uniqueLabels{i};
        labelIndices = strcmp(allData.labels, label);
        durations_by_label{i} = allData.durations(labelIndices);
        group_labels = [group_labels, repmat({label}, 1, sum(labelIndices))];
    end
    all_durations = [durations_by_label{:}];
    boxplot(all_durations, group_labels);
    title('持续时间分布');
    ylabel('持续时间 (秒)');
    
    % 幅值分布对比
    subplot(2,2,2);
    amplitudes_by_label = {};
    for i = 1:length(uniqueLabels)
        label = uniqueLabels{i};
        labelIndices = strcmp(allData.labels, label);
        labelSignals = allData.signals(labelIndices);
        allAmps = [];
        for j = 1:length(labelSignals)
            allAmps = [allAmps; labelSignals{j}];
        end
        amplitudes_by_label{i} = allAmps;
    end
    
    % 绘制幅值分布直方图
    colors = lines(length(uniqueLabels));
    hold on;
    for i = 1:length(uniqueLabels)
        histogram(amplitudes_by_label{i}, 30, 'FaceAlpha', 0.5, ...
                 'FaceColor', colors(i,:), 'DisplayName', uniqueLabels{i});
    end
    title('信号幅值分布');
    xlabel('幅值');
    ylabel('频次');
    legend('Location', 'best');
    
    % 段号分布
    subplot(2,2,3);
    segments_by_label = {};
    for i = 1:length(uniqueLabels)
        label = uniqueLabels{i};
        labelIndices = strcmp(allData.labels, label);
        segments_by_label{i} = allData.segments(labelIndices);
    end
    
    hold on;
    for i = 1:length(uniqueLabels)
        scatter(segments_by_label{i}, i*ones(size(segments_by_label{i})), ...
               100, colors(i,:), 'filled', 'DisplayName', uniqueLabels{i});
    end
    title('标注在各段的分布');
    xlabel('段号');
    ylabel('标签类型');
    yticks(1:length(uniqueLabels));
    yticklabels(uniqueLabels);
    legend('Location', 'best');
    
    % 时间轴分布
    subplot(2,2,4);
    hold on;
    for i = 1:length(allData.labels)
        label = allData.labels{i};
        labelIdx = find(strcmp(uniqueLabels, label));
        segment = allData.segments(i);
        
        % 计算在连续时间轴上的位置
        timePos = (segment - 1) * 60 + allData.durations(i)/2;
        scatter(timePos, labelIdx, 100, colors(labelIdx,:), 'filled');
    end
    title('标注在时间轴上的分布');
    xlabel('时间 (秒)');
    ylabel('标签类型');
    yticks(1:length(uniqueLabels));
    yticklabels(uniqueLabels);
    
    % 保存对比图
    % saveas(gcf, fullfile(processedDataDir, 'feature_comparison.png'));
    % fprintf('特征对比图已保存: %s\n', fullfile(processedDataDir, 'feature_comparison.png'));
end

%% 保存分析结果
% analysisResults = struct();
% analysisResults.labelStats = labelStats;
% analysisResults.totalSignals = length(dataFiles);
% analysisResults.uniqueLabels = uniqueLabels;
% analysisResults.analysisTime = datetime('now');
% 
% save(fullfile(processedDataDir, 'analysis_results.mat'), 'analysisResults');
% fprintf('\n分析结果已保存: %s\n', fullfile(processedDataDir, 'analysis_results.mat'));

fprintf('\n=== 示例演示完成 ===\n');
